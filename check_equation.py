# Chương trình kiểm tra phép toán a + b = c
# Input: Một dòng ghi phép toán (gồ<PERSON> đúng 9 ký tự)
# Output: YES nếu đúng, NO nếu sai

# Nhậ<PERSON> phép toán từ người dùng
equation = input()

# Tách các số từ chuỗi phép toán
# Format: "a + b = c" (9 ký tự)
a = int(equation[0])  # <PERSON><PERSON> thứ nhất ở vị trí 0
b = int(equation[4])  # S<PERSON> thứ hai ở vị trí 4 (sau "a + ")
c = int(equation[8])  # Kết quả ở vị trí 8 (sau "a + b = ")

# Kiểm tra phép toán
if a + b == c:
    print("YES")
else:
    print("NO")
